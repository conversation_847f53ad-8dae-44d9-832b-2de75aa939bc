"use client";
import React, { useCallback, useState } from 'react'
import Footer from "../home/<USER>";
import Navbar from "./navbar";
import JewelleryProductList from "./product-listing";
import Loader from "../../components/common/loader"
import Image from "next/image";
import { useRouter } from "next/navigation";
import { poppins } from "@/src/common/helper";
import { IoIosArrowDown } from "react-icons/io";
import SearchBar from "../common/searchBar";
import { debounce } from "lodash";
import { Modal, Checkbox, Radio, Slider, Button, Badge } from "antd";
import { JEWELLERY_TYPE, RING_TYPE, EARRING_TYPE, NECKLACE_TYPE, BRACELET_TYPE } from "@/src/libs/constants";

// Sort options constant
const SORT_OPTIONS = [
    { value: "FEATURED", label: "Featured" },
    { value: "BEST_SELLING", label: "Best Selling" },
    { value: "PRICE_LOW_TO_HIGH", label: "Price: Low to High" },
    { value: "PRICE_HIGH_TO_LOW", label: "Price: High to Low" },
    { value: "DATE_NEW_TO_OLD", label: "Date: New to Old" },
    { value: "DATE_OLD_TO_NEW", label: "Date: Old to New" },
    { value: "ALPHABETICALLY_Z_TO_A", label: "Alphabetically: Z to A" },
    { value: "ALPHABETICALLY_A_TO_Z", label: "Alphabetically: A to Z" },
];

const Jewellery = () => {
    const router = useRouter();
    const [search, setSearch] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(search);

    // Modal states
    const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
    const [isSortModalOpen, setIsSortModalOpen] = useState(false);

    // Filter states
    const [selectedJewelleryTypes, setSelectedJewelleryTypes] = useState<string[]>([]);
    const [selectedRingTypes, setSelectedRingTypes] = useState<string[]>([]);
    const [selectedEarringTypes, setSelectedEarringTypes] = useState<string[]>([]);
    const [selectedNecklaceTypes, setSelectedNecklaceTypes] = useState<string[]>([]);
    const [selectedBraceletTypes, setSelectedBraceletTypes] = useState<string[]>([]);
    const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);

    // Sort state
    const [selectedSort, setSelectedSort] = useState<string>("FEATURED");

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query);
            // fecthList(1, query);
        }, 500),
        []
    );

    // Check if any filters are applied
    const hasActiveFilters = () => {
        return (
            selectedJewelleryTypes.length > 0 ||
            selectedRingTypes.length > 0 ||
            selectedEarringTypes.length > 0 ||
            selectedNecklaceTypes.length > 0 ||
            selectedBraceletTypes.length > 0 ||
            priceRange[0] > 0 ||
            priceRange[1] < 10000
        );
    };

    // Check if sort is applied (not default)
    const hasActiveSort = () => {
        return selectedSort !== "FEATURED";
    };

    // Reset all filters
    const resetFilters = () => {
        setSelectedJewelleryTypes([]);
        setSelectedRingTypes([]);
        setSelectedEarringTypes([]);
        setSelectedNecklaceTypes([]);
        setSelectedBraceletTypes([]);
        setPriceRange([0, 10000]);
    };

    // Apply filters (close modal)
    const applyFilters = () => {
        setIsFilterModalOpen(false);
        // Here you would typically trigger a data fetch with the applied filters
        console.log("Applied filters:", {
            jewelleryTypes: selectedJewelleryTypes,
            ringTypes: selectedRingTypes,
            earringTypes: selectedEarringTypes,
            necklaceTypes: selectedNecklaceTypes,
            braceletTypes: selectedBraceletTypes,
            priceRange
        });
    };

    // Apply sort (close modal)
    const applySort = () => {
        setIsSortModalOpen(false);
        // Here you would typically trigger a data fetch with the applied sort
        console.log("Applied sort:", selectedSort);
    };

    return (
        <div className={`w-auto h-auto space-y-14 select-none ${poppins.className}`}>
            {/* Section 1: Hero Section */}
            <div className="relative w-full h-[90vh]">
                {/* Navbar on top of image */}
                <div className="absolute top-0 left-0 w-full z-10">
                    <Navbar />
                </div>

                {/* Hero Image */}
                <Image
                    src="/assets/jewellery/banner.svg"
                    alt="Shreeji Gems Logo"
                    fill
                    className="object-cover"
                    priority
                />
            </div>

            {/* Section 2: Filter, Search & Sort */}
            <div className="flex justify-between items-center gap-6 px-10">
                {/* Filter */}
                <Badge dot={hasActiveFilters()} offset={[-5, 5]}>
                    <div
                        className="flex border-2 border-black rounded-[50px] px-5 justify-center items-center cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => setIsFilterModalOpen(true)}
                    >
                        <Image
                            src="/assets/jewellery/filter-icon.svg"
                            alt="Filter"
                            width={24}
                            height={24}
                            className=""
                        />
                        <p className="px-2 font-[500]">Filter</p>
                        <IoIosArrowDown className="h-[20px] w-auto cursor-pointer mt-1" />
                    </div>
                </Badge>

                {/* Search */}
                <div className="flex min-w-[550px] border-2 border-black rounded-[50px] px-3 py-1 justify-center items-center">
                    <SearchBar {...{ search, setSearch, handleSearch }} />
                </div>

                {/* Sort */}
                <Badge dot={hasActiveSort()} offset={[-5, 5]}>
                    <div
                        className="flex border-2 border-black rounded-[50px] px-5 justify-center items-center cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => setIsSortModalOpen(true)}
                    >
                        <Image
                            src="/assets/jewellery/filter-icon.svg"
                            alt="Sort"
                            width={24}
                            height={24}
                            className=""
                        />
                        <p className="px-2 font-[500]">Sort</p>
                        <IoIosArrowDown className="h-[20px] w-auto cursor-pointer mt-1" />
                    </div>
                </Badge>
            </div>

            {/* Section 3: Jewellery Items */}
            <div>
                <JewelleryProductList />
            </div>

            {/* Section 4: More loader */}
            <Loader />

            {/* Section : Footer Section */}
            <Footer />

            {/* Filter Modal */}
            <Modal
                title="Filter Products"
                open={isFilterModalOpen}
                onCancel={() => setIsFilterModalOpen(false)}
                footer={[
                    <Button key="reset" onClick={resetFilters}>
                        Reset All
                    </Button>,
                    <Button key="cancel" onClick={() => setIsFilterModalOpen(false)}>
                        Cancel
                    </Button>,
                    <Button key="apply" type="primary" onClick={applyFilters}>
                        Apply Filters
                    </Button>,
                ]}
                width={600}
                className={poppins.className}
            >
                <div className="space-y-6 py-4">
                    {/* Jewellery Type Filter */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Jewellery Type</h3>
                        <Checkbox.Group
                            value={selectedJewelleryTypes}
                            onChange={setSelectedJewelleryTypes}
                            className="grid grid-cols-2 gap-2"
                        >
                            {JEWELLERY_TYPE.map((type) => (
                                <Checkbox key={type.value} value={type.value}>
                                    {type.label}
                                </Checkbox>
                            ))}
                        </Checkbox.Group>
                    </div>

                    {/* Ring Type Filter */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Ring Types</h3>
                        <Checkbox.Group
                            value={selectedRingTypes}
                            onChange={setSelectedRingTypes}
                            className="grid grid-cols-1 gap-2"
                        >
                            {RING_TYPE.map((type) => (
                                <Checkbox key={type.value} value={type.value}>
                                    {type.label}
                                </Checkbox>
                            ))}
                        </Checkbox.Group>
                    </div>

                    {/* Earring Type Filter */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Earring Types</h3>
                        <Checkbox.Group
                            value={selectedEarringTypes}
                            onChange={setSelectedEarringTypes}
                            className="grid grid-cols-1 gap-2"
                        >
                            {EARRING_TYPE.map((type) => (
                                <Checkbox key={type.value} value={type.value}>
                                    {type.label}
                                </Checkbox>
                            ))}
                        </Checkbox.Group>
                    </div>

                    {/* Necklace Type Filter */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Necklace Types</h3>
                        <Checkbox.Group
                            value={selectedNecklaceTypes}
                            onChange={setSelectedNecklaceTypes}
                            className="grid grid-cols-1 gap-2"
                        >
                            {NECKLACE_TYPE.map((type) => (
                                <Checkbox key={type.value} value={type.value}>
                                    {type.label}
                                </Checkbox>
                            ))}
                        </Checkbox.Group>
                    </div>

                    {/* Bracelet Type Filter */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Bracelet Types</h3>
                        <Checkbox.Group
                            value={selectedBraceletTypes}
                            onChange={setSelectedBraceletTypes}
                            className="grid grid-cols-1 gap-2"
                        >
                            {BRACELET_TYPE.map((type) => (
                                <Checkbox key={type.value} value={type.value}>
                                    {type.label}
                                </Checkbox>
                            ))}
                        </Checkbox.Group>
                    </div>

                    {/* Price Range Filter */}
                    <div>
                        <h3 className="text-lg font-semibold mb-3">Price Range</h3>
                        <div className="px-4">
                            <Slider
                                range
                                min={0}
                                max={10000}
                                step={100}
                                value={priceRange}
                                onChange={(value) => setPriceRange(value as [number, number])}
                                tooltip={{
                                    formatter: (value) => `₹${value}`
                                }}
                            />
                            <div className="flex justify-between text-sm text-gray-600 mt-2">
                                <span>₹{priceRange[0]}</span>
                                <span>₹{priceRange[1]}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>

            {/* Sort Modal */}
            <Modal
                title="Sort Products"
                open={isSortModalOpen}
                onCancel={() => setIsSortModalOpen(false)}
                footer={[
                    <Button key="cancel" onClick={() => setIsSortModalOpen(false)}>
                        Cancel
                    </Button>,
                    <Button key="apply" type="primary" onClick={applySort}>
                        Apply Sort
                    </Button>,
                ]}
                width={400}
                className={poppins.className}
            >
                <div className="py-4">
                    <Radio.Group
                        value={selectedSort}
                        onChange={(e) => setSelectedSort(e.target.value)}
                        className="w-full"
                    >
                        <div className="space-y-3">
                            {SORT_OPTIONS.map((option) => (
                                <Radio key={option.value} value={option.value} className="block">
                                    {option.label}
                                </Radio>
                            ))}
                        </div>
                    </Radio.Group>
                </div>
            </Modal>
        </div>
    )
}

export default Jewellery
