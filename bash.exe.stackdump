Stack trace:
Frame         Function      Args
0007FFFFABB0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF9AB0) msys-2.0.dll+0x2118E
0007FFFFABB0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFAE88) msys-2.0.dll+0x69BA
0007FFFFABB0  0002100469F2 (00021028DF99, 0007FFFFAA68, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFABB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFABB0  00021006A545 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFAE90  00021006B9A5 (0007FFFFABC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF73960000 ntdll.dll
7FFF73150000 KERNEL32.DLL
7FFF70F00000 KERNELBASE.dll
7FFF73440000 USER32.dll
7FFF70AB0000 win32u.dll
7FFF71E70000 GDI32.dll
7FFF71510000 gdi32full.dll
7FFF71650000 msvcp_win.dll
7FFF70AE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF73610000 advapi32.dll
7FFF72F00000 msvcrt.dll
7FFF71CC0000 sechost.dll
7FFF73800000 RPCRT4.dll
7FFF700B0000 CRYPTBASE.DLL
7FFF712F0000 bcryptPrimitives.dll
7FFF732A0000 IMM32.DLL
